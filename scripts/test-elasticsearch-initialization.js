/**
 * 测试ElasticSearch初始化优化的脚本
 * 运行方式: node scripts/test-elasticsearch-initialization.js
 */

// 模拟ElasticSearch客户端
class MockElasticsearchClient {
  constructor(options = {}) {
    this.shouldFail = options.shouldFail || false;
    this.delay = options.delay || 100;
  }

  async ping() {
    await this.simulateDelay();
    if (this.shouldFail) {
      throw new Error('Connection failed');
    }
    return { statusCode: 200 };
  }

  get indices() {
    return {
      exists: async () => {
        await this.simulateDelay();
        return !this.shouldFail;
      },
      create: async () => {
        await this.simulateDelay();
        if (this.shouldFail) {
          throw new Error('Index creation failed');
        }
        return { acknowledged: true };
      }
    };
  }

  async index(params) {
    await this.simulateDelay();
    if (this.shouldFail) {
      throw new Error('Index operation failed');
    }
    return {
      _id: params.id || 'test-id',
      result: 'created'
    };
  }

  async simulateDelay() {
    return new Promise(resolve => setTimeout(resolve, this.delay));
  }
}

// 简化版的ElasticSearch服务用于测试
class TestElasticsearchService {
  constructor(clientOptions = {}) {
    this.mockClient = new MockElasticsearchClient(clientOptions);
    this.promptsIndex = 'test-prompts';
    this.evaluationsIndex = 'test-evaluations';

    // 模拟真实的ES客户端结构
    this.client = {
      ping: () => this.mockClient.ping(),
      index: (params) => this.mockClient.index(params),
      indices: {
        exists: (params) => this.mockClient.indices.exists(params),
        create: (params) => this.mockClient.indices.create(params)
      }
    };

    // 初始化状态管理
    this.initializationPromise = null;
    this.isInitialized = false;
    this.initializationError = null;

    // 启动异步初始化（不阻塞构造函数）
    this.startInitialization();
  }

  startInitialization() {
    console.log('🚀 开始异步初始化...');
    this.initializationPromise = this.initializeIndices()
      .then(() => {
        this.isInitialized = true;
        console.log('✅ ElasticSearch索引初始化完成');
      })
      .catch((error) => {
        this.initializationError = error;
        console.error('❌ ElasticSearch索引初始化失败:', error.message);
      });
  }

  async initializeIndices() {
    try {
      // 模拟检查和创建索引
      const promptsExists = await this.client.indices.exists({ index: this.promptsIndex });
      if (!promptsExists) {
        await this.client.indices.create({ index: this.promptsIndex });
      }

      const evaluationsExists = await this.client.indices.exists({ index: this.evaluationsIndex });
      if (!evaluationsExists) {
        await this.client.indices.create({ index: this.evaluationsIndex });
      }
    } catch (error) {
      console.error('索引初始化过程中出错:', error.message);
      throw error;
    }
  }

  async ensureInitialized() {
    if (this.isInitialized) {
      return true;
    }

    if (this.initializationError) {
      throw new Error(`ElasticSearch初始化失败: ${this.initializationError.message}`);
    }

    if (this.initializationPromise) {
      await this.initializationPromise;
      return true;
    }

    // 如果没有初始化Promise，重新启动初始化
    this.startInitialization();
    await this.initializationPromise;
    return true;
  }

  async savePrompt(promptData) {
    try {
      console.log('📝 保存提示词请求...');
      
      // 确保索引已初始化
      await this.ensureInitialized();

      const result = await this.client.index({
        index: this.promptsIndex,
        id: promptData.id,
        body: promptData
      });

      console.log('✅ 提示词保存成功');
      return result;
    } catch (error) {
      console.error('❌ 保存提示词失败:', error.message);
      throw error;
    }
  }

  getStatus() {
    if (this.isInitialized) {
      return 'ready';
    } else if (this.initializationError) {
      return 'failed';
    } else {
      return 'initializing';
    }
  }
}

// 测试场景
async function testScenario(name, testFn) {
  console.log(`\n🧪 测试场景: ${name}`);
  console.log('='.repeat(50));
  
  try {
    await testFn();
    console.log(`✅ ${name} - 测试通过`);
  } catch (error) {
    console.error(`❌ ${name} - 测试失败:`, error.message);
  }
}

// 测试函数
async function runTests() {
  console.log('🔬 ElasticSearch初始化优化测试\n');

  // 场景1：正常初始化
  await testScenario('正常初始化流程', async () => {
    const service = new TestElasticsearchService({ delay: 200 });
    
    // 立即检查状态（应该是initializing）
    console.log('初始状态:', service.getStatus());
    
    // 尝试保存数据（应该等待初始化完成）
    const startTime = Date.now();
    await service.savePrompt({ id: 'test-1', content: '测试内容' });
    const endTime = Date.now();
    
    console.log(`操作耗时: ${endTime - startTime}ms`);
    console.log('最终状态:', service.getStatus());
    
    if (service.getStatus() !== 'ready') {
      throw new Error('服务状态不正确');
    }
  });

  // 场景2：初始化失败
  await testScenario('初始化失败处理', async () => {
    const service = new TestElasticsearchService({ shouldFail: true, delay: 100 });
    
    // 等待初始化完成（失败）
    await new Promise(resolve => setTimeout(resolve, 200));
    
    console.log('初始化后状态:', service.getStatus());
    
    try {
      await service.savePrompt({ id: 'test-2', content: '测试内容' });
      throw new Error('应该抛出错误');
    } catch (error) {
      if (error.message.includes('初始化失败') || error.message.includes('Index operation failed')) {
        console.log('正确捕获初始化失败错误');
      } else {
        throw error;
      }
    }
  });

  // 场景3：并发请求
  await testScenario('并发请求处理', async () => {
    const service = new TestElasticsearchService({ delay: 300 });
    
    // 立即发起多个并发请求
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(
        service.savePrompt({ id: `test-${i}`, content: `测试内容${i}` })
      );
    }
    
    const startTime = Date.now();
    const results = await Promise.all(promises);
    const endTime = Date.now();
    
    console.log(`并发请求完成，耗时: ${endTime - startTime}ms`);
    console.log(`成功处理 ${results.length} 个请求`);
    
    if (results.length !== 5) {
      throw new Error('并发请求处理不完整');
    }
  });

  // 场景4：重复调用ensureInitialized
  await testScenario('重复初始化检查', async () => {
    const service = new TestElasticsearchService({ delay: 100 });
    
    // 等待初始化完成
    await service.ensureInitialized();
    console.log('首次初始化完成');
    
    // 多次调用ensureInitialized（应该立即返回）
    const startTime = Date.now();
    await service.ensureInitialized();
    await service.ensureInitialized();
    await service.ensureInitialized();
    const endTime = Date.now();
    
    console.log(`重复调用耗时: ${endTime - startTime}ms`);
    
    if (endTime - startTime > 50) {
      throw new Error('重复调用耗时过长');
    }
  });

  // 场景5：构造函数非阻塞
  await testScenario('构造函数非阻塞', async () => {
    const startTime = Date.now();
    
    // 创建多个服务实例（应该立即返回）
    const services = [];
    for (let i = 0; i < 3; i++) {
      services.push(new TestElasticsearchService({ delay: 500 }));
    }
    
    const constructorTime = Date.now() - startTime;
    console.log(`创建3个服务实例耗时: ${constructorTime}ms`);
    
    if (constructorTime > 100) {
      throw new Error('构造函数阻塞时间过长');
    }
    
    // 验证所有服务都能正常工作
    const promises = services.map((service, index) => 
      service.savePrompt({ id: `multi-${index}`, content: `内容${index}` })
    );
    
    await Promise.all(promises);
    console.log('所有服务实例都正常工作');
  });

  console.log('\n🎉 所有测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { TestElasticsearchService };
